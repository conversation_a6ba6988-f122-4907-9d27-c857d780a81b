#!/usr/bin/env python3
"""
CI-friendly test suite for chatbot changes that doesn't require:
- Running database
- Docker
- External services

This focuses on code structure, imports, and model definitions.
"""

import os
import sys
import importlib.util

# Add the parent directory to the path so we can import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def test_database_model_structure():
    """Test that the database model has the correct structure"""
    print("🔍 Testing Database Model Structure...")
    try:
        from app.models import Chatbot
        
        # Check for new fields
        new_fields = ['welcome_message', 'thank_you_message', 'entity_type']
        old_fields = ['connected_account_entity_type', 'connected_account_id']
        
        for field in new_fields:
            if hasattr(Chatbot, field):
                print(f"  ✅ {field} field found in Chatbot model")
            else:
                print(f"  ❌ {field} field missing from Chatbot model")
                return False
        
        # Check that old fields are removed
        for field in old_fields:
            if hasattr(Chatbot, field):
                print(f"  ⚠️  Old field {field} still exists (should be removed)")
            else:
                print(f"  ✅ Old field {field} properly removed")
        
        print("✅ Database model structure test passed")
        return True
        
    except ImportError as e:
        print(f"❌ Database model test failed: Could not import Chatbot model - {e}")
        return False
    except Exception as e:
        print(f"❌ Database model test failed: {e}")
        return False


def test_pydantic_model_structure():
    """Test that Pydantic models have the correct structure"""
    print("🔍 Testing Pydantic Model Structure...")
    try:
        from app.models import ChatbotCreate, ChatbotUpdate, ConnectedAccount
        
        # Test ConnectedAccount model
        print("  Testing ConnectedAccount model...")
        required_fields = ['displayName', 'entityType', 'accountId']
        
        # Create a test instance
        test_account = ConnectedAccount(
            displayName="Test Account",
            entityType="CUSTOMER",
            accountId=12345
        )
        
        for field in required_fields:
            if hasattr(test_account, field):
                print(f"    ✅ {field} field found in ConnectedAccount")
            else:
                print(f"    ❌ {field} field missing from ConnectedAccount")
                return False
        
        # Test ChatbotCreate model
        print("  Testing ChatbotCreate model...")
        test_chatbot_data = {
            "name": "Test Bot",
            "type": "AI",
            "description": "Test description",
            "welcomeMessage": "Welcome!",
            "thankYouMessage": "Thank you!",
            "connectedAccount": test_account
        }
        
        try:
            chatbot = ChatbotCreate(**test_chatbot_data)
            print("    ✅ ChatbotCreate accepts new structure")
            
            # Check if fields are accessible
            if hasattr(chatbot, 'welcomeMessage'):
                print(f"    ✅ welcomeMessage: {chatbot.welcomeMessage}")
            if hasattr(chatbot, 'thankYouMessage'):
                print(f"    ✅ thankYouMessage: {chatbot.thankYouMessage}")
            if hasattr(chatbot, 'connectedAccount'):
                print(f"    ✅ connectedAccount: {chatbot.connectedAccount.displayName}")
                
        except Exception as e:
            print(f"    ❌ ChatbotCreate validation failed: {e}")
            return False
        
        print("✅ Pydantic model structure test passed")
        return True
        
    except ImportError as e:
        print(f"❌ Pydantic model test failed: Could not import models - {e}")
        return False
    except Exception as e:
        print(f"❌ Pydantic model test failed: {e}")
        return False


def test_service_layer_structure():
    """Test that service layer has correct methods and structure"""
    print("🔍 Testing Service Layer Structure...")
    try:
        from app.services.chatbot_service import ChatbotService
        
        service = ChatbotService()
        
        # Check for required methods
        required_methods = [
            'create_chatbot',
            'update_chatbot',
            'get_chatbot',
            'list_chatbots',
            'delete_chatbot',
            'find_chatbot_by_entity_and_trigger'
        ]
        
        for method_name in required_methods:
            if hasattr(service, method_name):
                print(f"  ✅ {method_name} method found")
            else:
                print(f"  ❌ {method_name} method missing")
                return False
        
        # Check method signatures
        import inspect
        
        # Check find_chatbot_by_entity_and_trigger signature
        method = getattr(service, 'find_chatbot_by_entity_and_trigger')
        sig = inspect.signature(method)
        params = list(sig.parameters.keys())
        expected_params = ['entity_type', 'connected_account_id', 'trigger', 'tenant_id']
        
        for param in expected_params:
            if param in params:
                print(f"  ✅ find_chatbot_by_entity_and_trigger has {param} parameter")
            else:
                print(f"  ❌ find_chatbot_by_entity_and_trigger missing {param} parameter")
                return False
        
        print("✅ Service layer structure test passed")
        return True
        
    except ImportError as e:
        print(f"❌ Service layer test failed: Could not import ChatbotService - {e}")
        return False
    except Exception as e:
        print(f"❌ Service layer test failed: {e}")
        return False


def test_router_structure():
    """Test that router has correct endpoints and structure"""
    print("🔍 Testing Router Structure...")
    try:
        # Import the router module
        from app.routers import chatbot
        
        # Check if the router has the required endpoints
        router = getattr(chatbot, 'router', None)
        if not router:
            print("  ❌ Router not found in chatbot module")
            return False
        
        print("  ✅ Router found in chatbot module")
        
        # Check for start_conversation function
        if hasattr(chatbot, 'start_conversation'):
            print("  ✅ start_conversation endpoint found")
            
            # Check function signature
            import inspect
            sig = inspect.signature(chatbot.start_conversation)
            params = list(sig.parameters.keys())
            
            if 'conversation_request' in params:
                print("  ✅ start_conversation accepts conversation_request parameter")
            else:
                print("  ❌ start_conversation missing conversation_request parameter")
                return False
                
        else:
            print("  ❌ start_conversation endpoint missing")
            return False
        
        print("✅ Router structure test passed")
        return True
        
    except ImportError as e:
        print(f"❌ Router test failed: Could not import chatbot router - {e}")
        return False
    except Exception as e:
        print(f"❌ Router test failed: {e}")
        return False


def test_conversation_request_model():
    """Test the ConversationRequest model structure"""
    print("🔍 Testing ConversationRequest Model...")
    try:
        from app.models import ConversationRequest
        
        # Test creating a conversation request
        test_data = {
            "message": "Hello, I need help",
            "entityType": "CUSTOMER",
            "connectedAccountId": 12345,
            "trigger": "NEW_ENTITY"
        }
        
        request = ConversationRequest(**test_data)
        
        # Check fields
        required_fields = ['message', 'entityType', 'connectedAccountId', 'trigger']
        for field in required_fields:
            if hasattr(request, field):
                print(f"  ✅ {field} field found in ConversationRequest")
            else:
                print(f"  ❌ {field} field missing from ConversationRequest")
                return False
        
        # Test trigger validation
        valid_triggers = ["NEW_ENTITY", "EXISTING_ENTITY"]
        for trigger in valid_triggers:
            try:
                test_request = ConversationRequest(
                    message="Test",
                    entityType="CUSTOMER",
                    connectedAccountId=123,
                    trigger=trigger
                )
                print(f"  ✅ Trigger '{trigger}' is valid")
            except Exception as e:
                print(f"  ❌ Trigger '{trigger}' validation failed: {e}")
                return False
        
        print("✅ ConversationRequest model test passed")
        return True
        
    except ImportError as e:
        print(f"❌ ConversationRequest test failed: Could not import model - {e}")
        return False
    except Exception as e:
        print(f"❌ ConversationRequest test failed: {e}")
        return False


def test_migration_files():
    """Test that migration files exist and have correct structure"""
    print("🔍 Testing Migration Files...")
    
    migration_dirs = ['alembic/versions', 'migrations']
    migration_found = False
    
    for migration_dir in migration_dirs:
        if os.path.exists(migration_dir):
            files = os.listdir(migration_dir)
            py_files = [f for f in files if f.endswith('.py') and f != '__init__.py']
            
            if py_files:
                print(f"  ✅ Found {len(py_files)} migration files in {migration_dir}")
                for file in py_files[:3]:  # Show first 3 files
                    print(f"    - {file}")
                if len(py_files) > 3:
                    print(f"    ... and {len(py_files) - 3} more")
                migration_found = True
                break
    
    if not migration_found:
        print("  ⚠️  No migration files found (this might be expected)")
    
    print("✅ Migration files test passed")
    return True


def test_imports_and_dependencies():
    """Test that all required imports work"""
    print("🔍 Testing Imports and Dependencies...")
    
    required_imports = [
        ('app.models', ['Chatbot', 'ChatbotCreate', 'ConversationRequest']),
        ('app.services.chatbot_service', ['ChatbotService']),
        ('app.routers.chatbot', ['router']),
        ('app.database', ['get_db']),
    ]
    
    for module_name, classes in required_imports:
        try:
            module = __import__(module_name, fromlist=classes)
            print(f"  ✅ Successfully imported {module_name}")
            
            for class_name in classes:
                if hasattr(module, class_name):
                    print(f"    ✅ {class_name} found in {module_name}")
                else:
                    print(f"    ⚠️  {class_name} not found in {module_name}")
                    
        except ImportError as e:
            print(f"❌ Import failed for {module_name}: {e}")
            return False
        
    print("✅ Imports and dependencies test passed")
    return True