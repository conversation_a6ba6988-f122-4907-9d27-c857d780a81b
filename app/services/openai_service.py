import os
import logging
from openai import OpenAI
from dotenv import load_dotenv
import json
import tiktoken
from fastapi import HTTPEx<PERSON>
from app.services.rate_limiter import rate_limiter

logger = logging.getLogger(__name__)

class OpenAIService:
    def __init__(self):
        # Load environment variables
        load_dotenv()

        # Initialize OpenAI client
        openai_api_key = os.getenv("OPENAI_API_KEY")
        if not openai_api_key:
            logger.warning("OPENAI_API_KEY not set in environment variables")

        self.openai_client = OpenAI(api_key=openai_api_key)

        # Initialize tokenizer for token estimation
        try:
            self.tokenizer = tiktoken.encoding_for_model("gpt-4")
        except Exception:
            self.tokenizer = tiktoken.get_encoding("cl100k_base")

        logger.info("Initialized OpenAI service with rate limiting")

    def estimate_tokens(self, text: str) -> int:
        """
        Estimate the number of tokens in a text string.

        Args:
            text: The text to estimate tokens for

        Returns:
            Estimated number of tokens
        """
        try:
            return len(self.tokenizer.encode(text))
        except Exception as e:
            logger.warning(f"Error estimating tokens: {e}")
            # Fallback: rough estimation (1 token ≈ 4 characters)
            return len(text) // 4

    def estimate_messages_tokens(self, messages: list) -> int:
        """
        Estimate tokens for a list of messages.

        Args:
            messages: List of message objects with role and content

        Returns:
            Estimated number of tokens
        """
        total_tokens = 0
        for message in messages:
            # Add tokens for role and content
            total_tokens += self.estimate_tokens(message.get("role", ""))
            total_tokens += self.estimate_tokens(message.get("content", ""))
            # Add overhead tokens per message (approximately 4 tokens per message)
            total_tokens += 4

        # Add overhead for the conversation (approximately 2-3 tokens)
        total_tokens += 3
        return total_tokens

    def generate_embedding(self, text, tenant_id: str = None):
        """
        Generate an embedding for the given text using OpenAI with rate limiting

        Args:
            text: The text to generate an embedding for
            tenant_id: The tenant ID for rate limiting

        Returns:
            The embedding vector
        """
        try:
            # Estimate tokens for rate limiting
            estimated_tokens = self.estimate_tokens(text)

            # Check rate limits if tenant_id is provided
            if tenant_id:
                rate_check = rate_limiter.check_rate_limit(tenant_id, estimated_tokens)
                if not rate_check["allowed"]:
                    logger.warning(f"Rate limit exceeded for tenant {tenant_id}: {rate_check['reason']}")
                    raise HTTPException(
                        status_code=429,
                        detail={
                            "error": "Rate limit exceeded",
                            "reason": rate_check["reason"],
                            "current_rpm": rate_check["current_rpm"],
                            "current_tpm": rate_check["current_tpm"],
                            "limit_rpm": rate_check["limit_rpm"],
                            "limit_tpm": rate_check["limit_tpm"],
                            "reset_time": rate_check["reset_time"].isoformat() if rate_check["reset_time"] else None
                        }
                    )

            # Make the API call
            response = self.openai_client.embeddings.create(
                model="text-embedding-3-small",
                input=text
            )

            # Record the request for rate limiting
            if tenant_id:
                # Embedding requests typically use fewer tokens than estimated
                actual_tokens = estimated_tokens
                rate_limiter.record_request(tenant_id, actual_tokens)

            return response.data[0].embedding

        except HTTPException:
            # Re-raise HTTP exceptions (rate limiting)
            raise
        except Exception as e:
            logger.error(f"Error generating embedding: {str(e)}")
            raise
    
    def generate_chat_response(self, messages, max_tokens=300, temperature=None, retry_count=0, tenant_id: str = None):
        """
        Generate a chat response using OpenAI with rate limiting

        Args:
            messages: List of message objects with role and content
            max_tokens: Maximum number of tokens to generate
            temperature: Temperature for response generation (ignored for o4-mini)
            retry_count: Number of retries attempted (for internal use)
            tenant_id: The tenant ID for rate limiting

        Returns:
            Tuple of (generated response text, input token count, output token count, model)
        """
        try:
            # Estimate tokens for rate limiting
            estimated_input_tokens = self.estimate_messages_tokens(messages)
            estimated_total_tokens = estimated_input_tokens + max_tokens

            # Check rate limits if tenant_id is provided
            if tenant_id:
                rate_check = rate_limiter.check_rate_limit(tenant_id, estimated_total_tokens)
                if not rate_check["allowed"]:
                    logger.warning(f"Rate limit exceeded for tenant {tenant_id}: {rate_check['reason']}")
                    return (
                        f"I'm sorry, but you've reached your usage limit. Please try again in a few minutes. "
                        f"(Reason: {rate_check['reason']})",
                        0, 0, "rate_limited"
                    )

            # Log the request for debugging
            logger.info(f"Generating chat response with {len(messages)} messages", extra={
                "first_message": messages[0]["content"][:100] if messages else "No messages",
                "max_tokens": max_tokens,
                "estimated_tokens": estimated_total_tokens,
                "tenant_id": tenant_id
            })

            # Check if OpenAI client is initialized
            if not hasattr(self, 'openai_client') or self.openai_client is None:
                openai_api_key = os.getenv("OPENAI_API_KEY")
                if not openai_api_key:
                    logger.error("OPENAI_API_KEY not set in environment variables")
                    return "I'm sorry, the OpenAI API key is not configured properly.", 0, 0, "unknown"

                self.openai_client = OpenAI(api_key=openai_api_key)

            # Make the API call - o4-mini doesn't support temperature parameter
            response = self.openai_client.chat.completions.create(
                model="o4-mini",
                messages=messages,
                # max_completion_tokens=max_tokens
            )

            # Extract token usage information
            usage = response.usage
            input_tokens = usage.prompt_tokens if usage else 0
            output_tokens = usage.completion_tokens if usage else 0
            total_tokens = input_tokens + output_tokens
            model = response.model if hasattr(response, 'model') else "o4-mini"

            # Record the request for rate limiting
            if tenant_id:
                rate_limiter.record_request(tenant_id, total_tokens)

            # Log successful response with token usage
            logger.info(f"Successfully generated chat response", extra={
                "input_tokens": input_tokens,
                "output_tokens": output_tokens,
                "total_tokens": total_tokens,
                "model": model,
                "tenant_id": tenant_id
            })
            print("RAW RESPONSE:")
            print(response.model_dump_json(indent=2))
            print("\nOUTPUT MESSAGE:")
            print(response.choices[0].message.content)
            
            # Check if the response is empty or too short
            content = response.choices[0].message.content
            if not content or len(content.strip()) < 5:  # Arbitrary minimum length
                logger.warning("OpenAI returned empty or very short response")
                return "I couldn't understand your question. Could you please rephrase it?", input_tokens, output_tokens, model

            return content, input_tokens, output_tokens, model
        except Exception as e:
            error_str = str(e)
            # Log detailed error information
            logger.error(f"Error generating chat response: {error_str}", exc_info=True, extra={
                "error_type": type(e).__name__,
                "messages_count": len(messages) if messages else 0
            })

            # Handle max_tokens error with retry
            if ("max_tokens" in error_str or "max_completion_tokens" in error_str) and retry_count < 2:
                # Increase max_tokens and retry
                new_max_tokens = max_tokens * 2
                logger.info(f"Retrying with increased max_completion_tokens: {new_max_tokens}")
                return self.generate_chat_response(messages, new_max_tokens, temperature, retry_count + 1)

            # Return a more specific error message based on the exception type
            if "RateLimitError" in error_str:
                return "I'm sorry, we've reached our API rate limit. Please try again in a moment.", 0, 0, "unknown"
            elif "AuthenticationError" in error_str:
                return "I'm sorry, there's an authentication issue with our AI service. Please contact support.", 0, 0, "unknown"
            elif "max_tokens" in error_str or "max_completion_tokens" in error_str:
                return "I'm sorry, your request is too complex. Please try a simpler question or break it into smaller parts.", 0, 0, "unknown"
            elif "temperature" in error_str:
                # Retry without temperature parameter
                if retry_count < 1:
                    logger.info("Retrying without temperature parameter")
                    return self.generate_chat_response(messages, max_tokens, None, retry_count + 1)
                return "I'm sorry, there was an issue with the request parameters. Please try again.", 0, 0, "unknown"
            elif "InvalidRequestError" in error_str:
                return "I'm sorry, there was an issue with the request format. Please try a simpler question.", 0, 0, "unknown"
            else:
                return "I couldn't understand your question. Could you please rephrase it?", 0, 0, "unknown"
    

    
    def is_conversation_ending(self, user_message):
        """
        Determine if a user message indicates they want to end the conversation

        Args:
            user_message: The user's message

        Returns:
            Tuple of (boolean indicating if the user wants to end the conversation, input_tokens, output_tokens, model)
        """
        try:
            # Create a prompt for the AI to determine if the user wants to end the conversation
            prompt = [
                {"role": "system", "content": "You are an AI assistant that determines if a user's message indicates they want to end a conversation. Return 'YES' if the message suggests ending the conversation (like 'goodbye', 'bye', 'end', 'quit', 'exit', 'thank you', etc.), and 'NO' if it doesn't."},
                {"role": "user", "content": f"User's message: {user_message}\n\nDoes this message indicate the user wants to end the conversation? Answer with YES or NO only."}
            ]
            
            # Use OpenAI to determine if the user wants to end the conversation
            response = self.openai_client.chat.completions.create(
                model="o4-mini",
                messages=prompt,
                # max_completion_tokens=10
            )

            result = response.choices[0].message.content.strip().upper()
            is_ending = "YES" in result

            # Extract token usage for logging
            usage = response.usage
            input_tokens = usage.prompt_tokens if usage else 0
            output_tokens = usage.completion_tokens if usage else 0

            logger.info(f"Conversation ending detection: {is_ending}", extra={
                "user_message": user_message,
                "is_ending": is_ending,
                "input_tokens": input_tokens,
                "output_tokens": output_tokens
            })

            return is_ending, input_tokens, output_tokens, "o4-mini"
        except Exception as e:
            logger.error(f"Error in conversation ending detection: {str(e)}")
            # Default to not ending in case of error
            return False, 0, 0, "unknown"

    def select_next_question(self, conversation_history, remaining_questions, answered_questions):
        """
        Select the next question to ask based on conversation history and remaining questions

        Args:
            conversation_history: List of conversation messages
            remaining_questions: List of questions that haven't been asked yet
            answered_questions: List of questions that have been answered

        Returns:
            Tuple of (selected_question_dict, input_tokens, output_tokens, model) or (None, 0, 0, "unknown") if no more questions
        """
        try:
            # Check if OpenAI client is initialized
            if not hasattr(self, 'openai_client') or self.openai_client is None:
                openai_api_key = os.getenv("OPENAI_API_KEY")
                if not openai_api_key:
                    logger.error("OPENAI_API_KEY not set in environment variables")
                    return None, 0, 0, "unknown"

                self.openai_client = OpenAI(api_key=openai_api_key)

            # If no remaining questions, return None
            if not remaining_questions:
                return None, 0, 0, "o4-mini"

            # Create context from conversation history
            conversation_context = ""
            for msg in conversation_history[-6:]:  # Last 6 messages for context
                role = msg.get("role", "")
                content = msg.get("content", "")
                if role in ["user", "assistant"]:
                    conversation_context += f"{role.capitalize()}: {content}\n"

            # Create context from answered questions
            answered_context = ""
            if answered_questions:
                answered_context = "Questions already answered:\n"
                for qa in answered_questions:
                    answered_context += f"Q: {qa.get('question', '')}\nA: {qa.get('answer', '')}\n"

            # Format remaining questions
            questions_list = ""
            for i, q in enumerate(remaining_questions, 1):
                questions_list += f"{i}. {q.get('question', '')}\n"

            # Create a prompt for selecting the next question
            prompt = [
                {
                    "role": "system",
                    "content": """You are a helpful assistant that selects the most appropriate next question to ask based on the conversation context.

                    Your task is to:
                    1. Analyze the conversation history and previously answered questions
                    2. Select the most logical next question from the remaining questions
                    3. Consider the natural flow of conversation and what would make sense to ask next
                    4. Respond with ONLY the number (1, 2, 3, etc.) of the question you want to select

                    Do not provide any explanation, just the number."""
                },
                {
                    "role": "user",
                    "content": f"""Conversation context:
{conversation_context}

{answered_context}

Remaining questions to choose from:
{questions_list}

Select the most appropriate next question by responding with its number (1, 2, 3, etc.)."""
                }
            ]

            # Use OpenAI to select the next question
            response = self.openai_client.chat.completions.create(
                model="o4-mini",
                messages=prompt,
                # max_completion_tokens=10
            )

            result = response.choices[0].message.content.strip()

            # Extract token usage for logging
            usage = response.usage
            input_tokens = usage.prompt_tokens if usage else 0
            output_tokens = usage.completion_tokens if usage else 0

            # Parse the result to get the question index
            try:
                selected_index = int(result) - 1  # Convert to 0-based index
                if 0 <= selected_index < len(remaining_questions):
                    selected_question = remaining_questions[selected_index]
                    logger.info(f"Selected next question: {selected_question.get('question', '')}", extra={
                        "selected_index": selected_index,
                        "input_tokens": input_tokens,
                        "output_tokens": output_tokens
                    })
                    return selected_question, input_tokens, output_tokens, "o4-mini"
                else:
                    logger.warning(f"Invalid question index selected: {selected_index}")
                    # Fallback to first remaining question
                    return remaining_questions[0], input_tokens, output_tokens, "o4-mini"
            except ValueError:
                logger.warning(f"Could not parse question selection: {result}")
                # Fallback to first remaining question
                return remaining_questions[0], input_tokens, output_tokens, "o4-mini"

        except Exception as e:
            logger.error(f"Error selecting next question: {str(e)}")
            # Fallback to first remaining question if available
            if remaining_questions:
                return remaining_questions[0], 0, 0, "unknown"
            return None, 0, 0, "unknown"

    def detect_conversation_termination(self, user_message, conversation_context=""):
        """
        Detect if the user wants to end the conversation

        Args:
            user_message: The user's latest message
            conversation_context: Recent conversation context for better understanding

        Returns:
            Tuple of (wants_to_end: bool, input_tokens: int, output_tokens: int, model: str)
        """
        try:
            # Check if OpenAI client is initialized
            if not hasattr(self, 'openai_client') or self.openai_client is None:
                openai_api_key = os.getenv("OPENAI_API_KEY")
                if not openai_api_key:
                    logger.error("OPENAI_API_KEY not set in environment variables")
                    return False, 0, 0, "unknown"

                self.openai_client = OpenAI(api_key=openai_api_key)

            # Create a prompt for detecting conversation termination intent
            prompt = [
                {
                    "role": "system",
                    "content": """You are an expert at detecting user intent. Your task is to determine if a user wants to end a conversation.

                    Look for these termination indicators:
                    - Explicit endings: "goodbye", "bye", "thanks, that's all", "no more questions", "I'm done", "that's it"
                    - Polite dismissals: "no thank you", "no thanks", "that's all I needed", "nothing else", "I'm good"
                    - Completion statements: "that's everything", "all set", "perfect", "got what I needed"
                    - Gratitude with finality: "thank you for your help", "thanks for everything", "appreciate it"

                    Do NOT consider these as termination:
                    - New questions or requests for information
                    - Requests for clarification
                    - Follow-up questions
                    - Expressions of confusion that need resolution

                    Respond with ONLY "YES" if the user wants to end the conversation, or "NO" if they want to continue.
                    Do not provide any explanation."""
                },
                {
                    "role": "user",
                    "content": f"""Conversation contextUser's message: "{user_message}"
                                   Does the user want to end the conversation? Respond with only YES or NO.
                                """
                }
            ]
            print(f"---------Promot-----------{prompt}")
            # Use OpenAI to detect termination intent
            response = self.openai_client.chat.completions.create(
                model="o4-mini",
                messages=prompt,
                # max_completion_tokens=500
            )
            print(f"=========Termination response {response.model_dump_json(indent=2)}")
            result = response.choices[0].message.content.strip().upper()

            # Extract token usage for logging
            usage = response.usage
            input_tokens = usage.prompt_tokens if usage else 0
            output_tokens = usage.completion_tokens if usage else 0

            # Parse the result
            wants_to_end = result == "YES"

            logger.info(f"Conversation termination detection: {wants_to_end}", extra={
                "user_message": user_message,
                "ai_decision": result,
                "input_tokens": input_tokens,
                "output_tokens": output_tokens
            })

            return wants_to_end, input_tokens, output_tokens, "o4-mini"

        except Exception as e:
            logger.error(f"Error detecting conversation termination: {str(e)}")
            # Conservative fallback - assume they don't want to end unless very obvious
            obvious_endings = ["bye", "goodbye", "thanks that's all", "no thanks", "that's all"]
            wants_to_end = any(ending in user_message.lower() for ending in obvious_endings)
            return wants_to_end, 0, 0, "unknown"

    def generate_farewell_message(self, user_message, conversation_summary=""):
        """
        Generate a polite farewell message when user wants to end the conversation

        Args:
            user_message: The user's termination message
            conversation_summary: Summary of what was discussed

        Returns:
            Tuple of (farewell_message: str, input_tokens: int, output_tokens: int, model: str)
        """
        try:
            # Check if OpenAI client is initialized
            if not hasattr(self, 'openai_client') or self.openai_client is None:
                openai_api_key = os.getenv("OPENAI_API_KEY")
                if not openai_api_key:
                    logger.error("OPENAI_API_KEY not set in environment variables")
                    return "Thank you for your time! Have a wonderful day and feel free to reach out anytime you need assistance.", 0, 0, "unknown"

                self.openai_client = OpenAI(api_key=openai_api_key)

            # Create a prompt for generating a farewell message
            prompt = [
                {
                    "role": "system",
                    "content": """You are a helpful and polite assistant. The user is ending the conversation. Generate a warm, professional farewell message that:

                    1. Acknowledges their decision to end the conversation
                    2. Thanks them for their time and interaction
                    3. Expresses that it was a pleasure helping them
                    4. Invites them to return if they need future assistance
                    5. Wishes them well

                    Keep the message concise but warm, professional, and genuinely appreciative."""
                },
                {
                    "role": "user",
                    "content": f"""The user said: "{user_message}"

Conversation summary: {conversation_summary}

Generate a polite farewell message that acknowledges their decision to end the conversation and thanks them warmly."""
                }
            ]

            # Use OpenAI to generate the farewell message
            response = self.openai_client.chat.completions.create(
                model="o4-mini",
                messages=prompt,
                # max_completion_tokens=500
            )

            farewell_message = response.choices[0].message.content.strip()

            # Extract token usage for logging
            usage = response.usage
            input_tokens = usage.prompt_tokens if usage else 0
            output_tokens = usage.completion_tokens if usage else 0

            logger.info("Generated farewell message", extra={
                "input_tokens": input_tokens,
                "output_tokens": output_tokens
            })

            return farewell_message, input_tokens, output_tokens, "o4-mini"

        except Exception as e:
            logger.error(f"Error generating farewell message: {str(e)}")
            return "Thank you for your time! Have a wonderful day and feel free to reach out anytime you need assistance.", 0, 0, "unknown"

    def generate_transition_message(self, answers):
        """
        Generate a polite transition message after the last question is answered

        Args:
            answers: List of question-answer pairs collected during the conversation

        Returns:
            A warm, appreciative transition message offering continued assistance
        """
        try:
            # Create an enhanced prompt for generating a polite transition message
            prompt = [
                {
                    "role": "system",
                    "content": """You are a helpful and polite assistant. The user has just completed answering all required questions.

                    Generate a warm, appreciative message that:
                    1. Thanks them sincerely for taking the time to provide the information
                    2. Acknowledges their cooperation
                    3. Offers continued assistance in a friendly way
                    4. Asks if they have any questions, need help with anything else, or if there's anything specific you can assist them with

                    Keep the tone professional but warm, and make them feel valued as a client."""
                },
                {
                    "role": "user",
                    "content": f"""The user has completed answering all our questions. Here's what they provided:
{json.dumps(answers, indent=2)}

Generate a polite transition message that thanks them for their time and information, and offers continued assistance. Ask if they have any questions or if there's anything else you can help them with today."""
                }
            ]
            
            # Use OpenAI to generate the transition message
            response = self.openai_client.chat.completions.create(
                model="o4-mini",
                messages=prompt,
                # max_completion_tokens=100
            )

            transition_message = response.choices[0].message.content.strip()

            # Extract token usage for logging
            usage = response.usage
            input_tokens = usage.prompt_tokens if usage else 0
            output_tokens = usage.completion_tokens if usage else 0

            logger.info("Generated transition message after last question", extra={
                "input_tokens": input_tokens,
                "output_tokens": output_tokens
            })
            
            return transition_message
        except Exception as e:
            logger.error(f"Error generating transition message: {str(e)}")
            # Return a polite default message in case of error
            return "Thank you so much for taking the time to provide all that information! I really appreciate your cooperation. Is there anything else I can help you with today? Feel free to ask me any questions you might have!"