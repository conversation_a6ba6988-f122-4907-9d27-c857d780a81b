"""
OpenAI Rate Limiter Service

This service implements rate limiting for OpenAI API calls based on:
- Tier 3 plan limits: 5,000 RPM (Requests Per Minute) and 300,000 TPM (Tokens Per Minute)
- 200 paid tenants
- Fair distribution of limits across tenants

Rate limits per tenant:
- RPM per tenant: 5,000 / 200 = 25 requests per minute
- TPM per tenant: 300,000 / 200 = 1,500 tokens per minute

Uses Redis for distributed rate limiting with sliding window algorithm.
"""

import os
import time
import json
import logging
from typing import Optional, Tuple, Dict
from datetime import datetime, timedelta
import redis
from redis import Redis
from dotenv import load_dotenv

load_dotenv()
logger = logging.getLogger(__name__)

class OpenAIRateLimiter:
    """
    Rate limiter for OpenAI API calls with tenant-based limits.
    
    Implements sliding window rate limiting using Redis.
    """
    
    def __init__(self):
        # OpenAI Tier 3 limits
        self.TOTAL_RPM = 5000  # Total requests per minute
        self.TOTAL_TPM = 300000  # Total tokens per minute
        self.TOTAL_TENANTS = 200  # Number of paid tenants
        
        # Per-tenant limits (fair distribution)
        self.RPM_PER_TENANT = self.TOTAL_RPM // self.TOTAL_TENANTS  # 25 RPM per tenant
        self.TPM_PER_TENANT = self.TOTAL_TPM // self.TOTAL_TENANTS  # 1,500 TPM per tenant
        
        # Buffer for burst handling (10% buffer)
        self.RPM_BUFFER = int(self.RPM_PER_TENANT * 0.1)
        self.TPM_BUFFER = int(self.TPM_PER_TENANT * 0.1)
        
        # Effective limits with buffer
        self.EFFECTIVE_RPM = self.RPM_PER_TENANT + self.RPM_BUFFER  # 27 RPM
        self.EFFECTIVE_TPM = self.TPM_PER_TENANT + self.TPM_BUFFER  # 1,650 TPM
        
        # Time windows
        self.WINDOW_SIZE = 60  # 1 minute in seconds
        self.CLEANUP_INTERVAL = 300  # Clean old entries every 5 minutes
        
        # Redis connection
        redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
        try:
            self.redis_client = redis.from_url(redis_url, decode_responses=True)
            # Test connection
            self.redis_client.ping()
            logger.info("Connected to Redis for rate limiting")
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            self.redis_client = None
    
    def _get_current_window(self) -> int:
        """Get current time window (minute)."""
        return int(time.time() // self.WINDOW_SIZE)
    
    def _get_redis_key(self, tenant_id: str, metric: str) -> str:
        """Generate Redis key for tenant and metric."""
        return f"rate_limit:{tenant_id}:{metric}"
    
    def _cleanup_old_entries(self, tenant_id: str):
        """Clean up old entries from Redis."""
        if not self.redis_client:
            return
        
        current_window = self._get_current_window()
        cutoff_window = current_window - 2  # Keep last 2 windows
        
        try:
            # Clean RPM entries
            rpm_key = self._get_redis_key(tenant_id, "rpm")
            self.redis_client.zremrangebyscore(rpm_key, 0, cutoff_window)
            
            # Clean TPM entries
            tpm_key = self._get_redis_key(tenant_id, "tpm")
            self.redis_client.zremrangebyscore(tpm_key, 0, cutoff_window)
            
        except Exception as e:
            logger.error(f"Error cleaning up rate limit entries: {e}")
    
    def _get_current_usage(self, tenant_id: str) -> Tuple[int, int]:
        """
        Get current usage for tenant in the current window.
        
        Returns:
            Tuple of (current_requests, current_tokens)
        """
        if not self.redis_client:
            return 0, 0
        
        current_window = self._get_current_window()
        
        try:
            # Get RPM usage
            rpm_key = self._get_redis_key(tenant_id, "rpm")
            rpm_count = self.redis_client.zcount(rpm_key, current_window, current_window)
            
            # Get TPM usage (sum of tokens in current window)
            tpm_key = self._get_redis_key(tenant_id, "tpm")
            tpm_entries = self.redis_client.zrangebyscore(
                tpm_key, current_window, current_window, withscores=True
            )
            tpm_count = sum(int(entry[1]) for entry in tpm_entries if len(entry) > 1)
            
            return rpm_count, tpm_count
            
        except Exception as e:
            logger.error(f"Error getting current usage: {e}")
            return 0, 0
    
    def check_rate_limit(self, tenant_id: str, estimated_tokens: int = 100) -> Dict:
        """
        Check if request is within rate limits.
        
        Args:
            tenant_id: The tenant ID
            estimated_tokens: Estimated tokens for the request
            
        Returns:
            Dict with rate limit status and information
        """
        if not self.redis_client:
            logger.warning("Redis not available, allowing request")
            return {
                "allowed": True,
                "reason": "rate_limiter_unavailable",
                "current_rpm": 0,
                "current_tpm": 0,
                "limit_rpm": self.EFFECTIVE_RPM,
                "limit_tpm": self.EFFECTIVE_TPM,
                "reset_time": None
            }
        
        try:
            # Clean up old entries periodically
            if int(time.time()) % self.CLEANUP_INTERVAL == 0:
                self._cleanup_old_entries(tenant_id)
            
            # Get current usage
            current_rpm, current_tpm = self._get_current_usage(tenant_id)
            
            # Check RPM limit
            if current_rpm >= self.EFFECTIVE_RPM:
                return {
                    "allowed": False,
                    "reason": "rpm_limit_exceeded",
                    "current_rpm": current_rpm,
                    "current_tpm": current_tpm,
                    "limit_rpm": self.EFFECTIVE_RPM,
                    "limit_tpm": self.EFFECTIVE_TPM,
                    "reset_time": self._get_reset_time()
                }
            
            # Check TPM limit
            if current_tpm + estimated_tokens > self.EFFECTIVE_TPM:
                return {
                    "allowed": False,
                    "reason": "tpm_limit_exceeded",
                    "current_rpm": current_rpm,
                    "current_tpm": current_tpm,
                    "limit_rpm": self.EFFECTIVE_RPM,
                    "limit_tpm": self.EFFECTIVE_TPM,
                    "reset_time": self._get_reset_time()
                }
            
            # Request is allowed
            return {
                "allowed": True,
                "reason": "within_limits",
                "current_rpm": current_rpm,
                "current_tpm": current_tpm,
                "limit_rpm": self.EFFECTIVE_RPM,
                "limit_tpm": self.EFFECTIVE_TPM,
                "reset_time": self._get_reset_time()
            }
            
        except Exception as e:
            logger.error(f"Error checking rate limit: {e}")
            # Allow request if there's an error
            return {
                "allowed": True,
                "reason": "rate_limiter_error",
                "current_rpm": 0,
                "current_tpm": 0,
                "limit_rpm": self.EFFECTIVE_RPM,
                "limit_tpm": self.EFFECTIVE_TPM,
                "reset_time": None
            }
    
    def record_request(self, tenant_id: str, actual_tokens: int):
        """
        Record a completed request for rate limiting.
        
        Args:
            tenant_id: The tenant ID
            actual_tokens: Actual tokens used in the request
        """
        if not self.redis_client:
            return
        
        current_window = self._get_current_window()
        current_time = time.time()
        
        try:
            # Record RPM (increment request count)
            rpm_key = self._get_redis_key(tenant_id, "rpm")
            self.redis_client.zadd(rpm_key, {str(current_time): current_window})
            self.redis_client.expire(rpm_key, self.WINDOW_SIZE * 2)  # Expire after 2 windows
            
            # Record TPM (add token count)
            tpm_key = self._get_redis_key(tenant_id, "tpm")
            self.redis_client.zadd(tpm_key, {str(current_time): actual_tokens})
            self.redis_client.expire(tpm_key, self.WINDOW_SIZE * 2)  # Expire after 2 windows
            
            logger.debug(f"Recorded request for tenant {tenant_id}: {actual_tokens} tokens")
            
        except Exception as e:
            logger.error(f"Error recording request: {e}")
    
    def _get_reset_time(self) -> datetime:
        """Get the time when rate limits reset (next minute)."""
        current_time = datetime.now()
        next_minute = current_time.replace(second=0, microsecond=0) + timedelta(minutes=1)
        return next_minute
    
    def get_tenant_stats(self, tenant_id: str) -> Dict:
        """
        Get detailed statistics for a tenant.
        
        Args:
            tenant_id: The tenant ID
            
        Returns:
            Dict with tenant statistics
        """
        if not self.redis_client:
            return {"error": "Redis not available"}
        
        try:
            current_rpm, current_tpm = self._get_current_usage(tenant_id)
            
            return {
                "tenant_id": tenant_id,
                "current_rpm": current_rpm,
                "current_tpm": current_tpm,
                "limit_rpm": self.EFFECTIVE_RPM,
                "limit_tpm": self.EFFECTIVE_TPM,
                "rpm_utilization": (current_rpm / self.EFFECTIVE_RPM) * 100,
                "tpm_utilization": (current_tpm / self.EFFECTIVE_TPM) * 100,
                "reset_time": self._get_reset_time().isoformat(),
                "window_size_seconds": self.WINDOW_SIZE
            }
            
        except Exception as e:
            logger.error(f"Error getting tenant stats: {e}")
            return {"error": str(e)}
    
    def get_global_stats(self) -> Dict:
        """Get global rate limiting statistics."""
        return {
            "total_rpm_limit": self.TOTAL_RPM,
            "total_tpm_limit": self.TOTAL_TPM,
            "total_tenants": self.TOTAL_TENANTS,
            "rpm_per_tenant": self.RPM_PER_TENANT,
            "tpm_per_tenant": self.TPM_PER_TENANT,
            "effective_rpm_per_tenant": self.EFFECTIVE_RPM,
            "effective_tpm_per_tenant": self.EFFECTIVE_TPM,
            "buffer_percentage": 10,
            "window_size_seconds": self.WINDOW_SIZE
        }

# Global rate limiter instance
rate_limiter = OpenAIRateLimiter()
