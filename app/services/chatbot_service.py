import logging
import uuid
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import func
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, UploadFile
from io import BytesIO
import PyPDF2
from app.database import get_db
from app.models import (
    Chatbot,
    ChatbotQuestion,
    ChatbotKnowledgebase,
    ChatbotConversation,
    Document,
    ChatbotCreate,
    ChatbotUpdate,
    QuestionCreate,
    QuestionUpdate
)
from app.services.rabbitmq_service import rabbitmq_service
from app.services.elasticsearch_service import ElasticsearchService
from app.services.s3_service import S3Service

logger = logging.getLogger(__name__)


class ChatbotService:
    """
    Service class for chatbot-related operations including usage collection
    """
    
    def __init__(self):
        """
        Initialize the ChatbotService
        """
        logger.info("Initialized ChatbotService")
    
    async def collect_and_publish_chatbot_usage(self):
        """
        Collect chatbot usage data and publish it to RabbitMQ
        
        This method:
        1. Queries the database for non-DRAFT chatbots grouped by tenant
        2. Formats the data for usage reporting
        3. Publishes the usage data to the RabbitMQ exchange
        """
        db = None
        try:
            logger.info("Starting chatbot usage collection...")
            
            db = next(get_db())
            
            # Get total non-Draft status chatbots by tenantId
            usage_data = (
                db.query(Chatbot.tenant_id, func.count(Chatbot.id))
                .filter(Chatbot.status != "DRAFT")
                .group_by(Chatbot.tenant_id)
                .all()
            )
            
            # Format the payload for usage reporting
            payload = [
                {
                    "tenantId": tenant_id,
                    "usageEntity": "CHATBOT",
                    "count": count
                }
                for tenant_id, count in usage_data
            ]
            
            # Publish the usage data if there's any data to publish
            if payload:
                await rabbitmq_service.publish_message(
                    "ex.usage",
                    "usage.collect.response",
                    payload
                )
                logger.info(f"Published chatbot usage data for {len(payload)} tenants: {payload}")
            else:
                logger.info("No chatbot usage data to publish.")
            
            logger.info("Chatbot usage collection completed successfully")
            
        except Exception as e:
            logger.error(f"Error collecting chatbot usage: {str(e)}")
            raise
        finally:
            if db:
                db.close()
    
    def get_chatbot_count_by_tenant(self, tenant_id: str, include_draft: bool = False) -> int:
        """
        Get the count of chatbots for a specific tenant
        
        Args:
            tenant_id: The tenant ID to get chatbot count for
            include_draft: Whether to include DRAFT status chatbots in the count
            
        Returns:
            int: Number of chatbots for the tenant
        """
        db = None
        try:
            db = next(get_db())
            
            query = db.query(func.count(Chatbot.id)).filter(Chatbot.tenant_id == tenant_id)
            
            if not include_draft:
                query = query.filter(Chatbot.status != "DRAFT")
            
            count = query.scalar()
            
            logger.info(f"Chatbot count for tenant {tenant_id}: {count} (include_draft: {include_draft})")
            return count
            
        except Exception as e:
            logger.error(f"Error getting chatbot count for tenant {tenant_id}: {str(e)}")
            raise
        finally:
            if db:
                db.close()
    
    def get_all_tenant_chatbot_counts(self, include_draft: bool = False) -> List[Dict[str, Any]]:
        """
        Get chatbot counts for all tenants
        
        Args:
            include_draft: Whether to include DRAFT status chatbots in the count
            
        Returns:
            List[Dict]: List of dictionaries containing tenant_id and count
        """
        db = None
        try:
            db = next(get_db())
            
            query = db.query(Chatbot.tenant_id, func.count(Chatbot.id)).group_by(Chatbot.tenant_id)
            
            if not include_draft:
                query = query.filter(Chatbot.status != "DRAFT")
            
            results = query.all()
            
            tenant_counts = [
                {
                    "tenant_id": tenant_id,
                    "count": count
                }
                for tenant_id, count in results
            ]
            
            logger.info(f"Retrieved chatbot counts for {len(tenant_counts)} tenants (include_draft: {include_draft})")
            return tenant_counts
            
        except Exception as e:
            logger.error(f"Error getting chatbot counts for all tenants: {str(e)}")
            raise
        finally:
            if db:
                db.close()
    
    def get_chatbot_by_id(self, chatbot_id: str, tenant_id: str) -> Optional[Chatbot]:
        """
        Get a chatbot by ID and tenant ID
        
        Args:
            chatbot_id: The chatbot ID
            tenant_id: The tenant ID
            
        Returns:
            Optional[Chatbot]: The chatbot if found, None otherwise
        """
        db = None
        try:
            db = next(get_db())
            
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()
            
            if chatbot:
                logger.info(f"Found chatbot {chatbot_id} for tenant {tenant_id}")
            else:
                logger.warning(f"Chatbot {chatbot_id} not found for tenant {tenant_id}")
            
            return chatbot
            
        except Exception as e:
            logger.error(f"Error getting chatbot {chatbot_id} for tenant {tenant_id}: {str(e)}")
            raise
        finally:
            if db:
                db.close()
    
    def get_chatbots_by_tenant(self, tenant_id: str, include_draft: bool = True) -> List[Chatbot]:
        """
        Get all chatbots for a specific tenant
        
        Args:
            tenant_id: The tenant ID
            include_draft: Whether to include DRAFT status chatbots
            
        Returns:
            List[Chatbot]: List of chatbots for the tenant
        """
        db = None
        try:
            db = next(get_db())
            
            query = db.query(Chatbot).filter(Chatbot.tenant_id == tenant_id)
            
            if not include_draft:
                query = query.filter(Chatbot.status != "DRAFT")
            
            chatbots = query.all()
            
            logger.info(f"Found {len(chatbots)} chatbots for tenant {tenant_id} (include_draft: {include_draft})")
            return chatbots
            
        except Exception as e:
            logger.error(f"Error getting chatbots for tenant {tenant_id}: {str(e)}")
            raise
        finally:
            if db:
                db.close()
    
    def get_chatbot_statistics(self, tenant_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get comprehensive chatbot statistics
        
        Args:
            tenant_id: Optional tenant ID to filter statistics for a specific tenant
            
        Returns:
            Dict[str, Any]: Dictionary containing various chatbot statistics
        """
        db = None
        try:
            db = next(get_db())
            
            stats = {}
            
            if tenant_id:
                # Statistics for a specific tenant
                total_query = db.query(func.count(Chatbot.id)).filter(Chatbot.tenant_id == tenant_id)
                active_query = db.query(func.count(Chatbot.id)).filter(
                    Chatbot.tenant_id == tenant_id,
                    Chatbot.status == "ACTIVE"
                )
                draft_query = db.query(func.count(Chatbot.id)).filter(
                    Chatbot.tenant_id == tenant_id,
                    Chatbot.status == "DRAFT"
                )
                
                stats = {
                    "tenant_id": tenant_id,
                    "total_chatbots": total_query.scalar(),
                    "active_chatbots": active_query.scalar(),
                    "draft_chatbots": draft_query.scalar()
                }
            else:
                # Global statistics
                total_chatbots = db.query(func.count(Chatbot.id)).scalar()
                active_chatbots = db.query(func.count(Chatbot.id)).filter(Chatbot.status == "ACTIVE").scalar()
                draft_chatbots = db.query(func.count(Chatbot.id)).filter(Chatbot.status == "DRAFT").scalar()
                inactive_chatbots = db.query(func.count(Chatbot.id)).filter(Chatbot.status == "INACTIVE").scalar()
                
                # Count by tenant
                tenant_counts = db.query(Chatbot.tenant_id, func.count(Chatbot.id)).group_by(Chatbot.tenant_id).all()
                
                stats = {
                    "total_chatbots": total_chatbots,
                    "active_chatbots": active_chatbots,
                    "draft_chatbots": draft_chatbots,
                    "inactive_chatbots": inactive_chatbots,
                    "total_tenants": len(tenant_counts),
                    "tenant_breakdown": [
                        {"tenant_id": tenant_id, "count": count}
                        for tenant_id, count in tenant_counts
                    ]
                }
            
            logger.info(f"Generated chatbot statistics: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"Error getting chatbot statistics: {str(e)}")
            raise
        finally:
            if db:
                db.close()

    # CRUD Operations

    def create_chatbot(self, chatbot_data: ChatbotCreate, tenant_id: str) -> Dict[str, Any]:
        """
        Create a new chatbot

        Args:
            chatbot_data: ChatbotCreate model with chatbot information
            tenant_id: The tenant ID

        Returns:
            Dict[str, Any]: Created chatbot information
        """
        db = None
        try:
            db = next(get_db())

            # Validate type value
            chatbot_type = chatbot_data.type.upper()
            if chatbot_type not in ["AI", "RULE"]:
                raise HTTPException(status_code=400, detail="Chatbot type must be either 'AI' or 'RULE'")

            # Generate unique chatbot ID
            chatbot_id = str(uuid.uuid4())

            # Extract connected account information
            connected_account_display_name = None
            entity_type = None
            connected_account_id = None

            if chatbot_data.connectedAccount:
                connected_account_display_name = chatbot_data.connectedAccount.displayName
                entity_type = chatbot_data.connectedAccount.entityType
                connected_account_id = chatbot_data.connectedAccount.accountId

            # Validate trigger value if provided
            trigger = None
            if chatbot_data.trigger:
                if chatbot_data.trigger not in ["NEW_ENTITY", "EXISTING_ENTITY"]:
                    raise HTTPException(status_code=400, detail="Trigger must be either 'NEW_ENTITY' or 'EXISTING_ENTITY'")
                trigger = chatbot_data.trigger

            # Create chatbot with minimal information
            chatbot = Chatbot(
                id=chatbot_id,
                tenant_id=tenant_id,
                name=chatbot_data.name,
                type=chatbot_type,
                description=chatbot_data.description or "",
                welcome_message=chatbot_data.welcomeMessage,
                thank_you_message=chatbot_data.thankYouMessage,
                connected_account_display_name=connected_account_display_name,
                entity_type=entity_type,
                connected_account_id=connected_account_id,
                trigger=trigger,
                status="DRAFT"  # Initial status is DRAFT until fully configured
            )

            # Add chatbot to database
            db.add(chatbot)
            db.commit()
            db.refresh(chatbot)

            logger.info(f"Created chatbot {chatbot_id} for tenant {tenant_id}")

            # Build connected account response
            connected_account = None
            if (chatbot.connected_account_display_name or
                chatbot.entity_type or
                chatbot.connected_account_id):
                connected_account = {
                    "displayName": chatbot.connected_account_display_name,
                    "entityType": chatbot.entity_type,
                    "accountId": chatbot.connected_account_id
                }

            return {
                "id": chatbot.id,
                "tenant_id": chatbot.tenant_id,
                "name": chatbot.name,
                "type": chatbot.type,
                "description": chatbot.description,
                "welcomeMessage": chatbot.welcome_message,
                "thankYouMessage": chatbot.thank_you_message,
                "connectedAccount": connected_account,
                "trigger": chatbot.trigger,
                "status": chatbot.status,
                "created_at": chatbot.created_at
            }

        except HTTPException:
            raise
        except Exception as e:
            if db:
                db.rollback()
            logger.error(f"Error creating chatbot: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error creating chatbot: {str(e)}")
        finally:
            if db:
                db.close()

    def update_chatbot(self, chatbot_id: str, chatbot_data: ChatbotUpdate, tenant_id: str) -> Dict[str, Any]:
        """
        Update an existing chatbot

        Args:
            chatbot_id: The chatbot ID to update
            chatbot_data: ChatbotUpdate model with updated information
            tenant_id: The tenant ID

        Returns:
            Dict[str, Any]: Updated chatbot information
        """
        db = None
        try:
            db = next(get_db())

            # Check if chatbot exists
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()

            if not chatbot:
                raise HTTPException(status_code=404, detail="Chatbot not found")

            # Validate knowledgebase IDs if provided
            if chatbot_data.knowledgebase_ids is not None:
                for kb_id in chatbot_data.knowledgebase_ids:
                    kb = db.query(Document).filter(
                        Document.id == kb_id,
                        Document.tenant_id == tenant_id
                    ).first()
                    if not kb:
                        raise HTTPException(status_code=404, detail=f"Knowledgebase with ID {kb_id} not found")

            # Update chatbot fields if provided
            if chatbot_data.name is not None:
                chatbot.name = chatbot_data.name

            if chatbot_data.description is not None:
                chatbot.description = chatbot_data.description

            if chatbot_data.welcomeMessage is not None:
                chatbot.welcome_message = chatbot_data.welcomeMessage

            if chatbot_data.thankYouMessage is not None:
                chatbot.thank_you_message = chatbot_data.thankYouMessage

            if chatbot_data.connectedAccount is not None:
                chatbot.connected_account_display_name = chatbot_data.connectedAccount.displayName
                chatbot.entity_type = chatbot_data.connectedAccount.entityType
                chatbot.connected_account_id = chatbot_data.connectedAccount.accountId

            if chatbot_data.trigger is not None:
                if chatbot_data.trigger not in ["NEW_ENTITY", "EXISTING_ENTITY"]:
                    raise HTTPException(status_code=400, detail="Trigger must be either 'NEW_ENTITY' or 'EXISTING_ENTITY'")
                chatbot.trigger = chatbot_data.trigger

            # Update questions if provided
            if chatbot_data.questions is not None:
                # Delete existing questions
                db.query(ChatbotQuestion).filter(
                    ChatbotQuestion.chatbot_id == chatbot_id,
                    ChatbotQuestion.tenant_id == tenant_id
                ).delete()

                # Add new questions
                for q_data in chatbot_data.questions:
                    question = ChatbotQuestion(
                        id=str(uuid.uuid4()),
                        chatbot_id=chatbot_id,
                        tenant_id=tenant_id,
                        question=q_data.question,
                        field_id=q_data.fieldId,
                        display_name=q_data.displayName
                    )
                    db.add(question)

            # Update knowledgebase associations if provided
            if chatbot_data.knowledgebase_ids is not None:
                # Delete existing associations
                db.query(ChatbotKnowledgebase).filter(
                    ChatbotKnowledgebase.chatbot_id == chatbot_id,
                    ChatbotKnowledgebase.tenant_id == tenant_id
                ).delete()

                # Add new associations
                for kb_id in chatbot_data.knowledgebase_ids:
                    kb_assoc = ChatbotKnowledgebase(
                        id=str(uuid.uuid4()),
                        chatbot_id=chatbot_id,
                        document_id=kb_id,
                        tenant_id=tenant_id
                    )
                    db.add(kb_assoc)

            # Commit changes
            db.commit()
            db.refresh(chatbot)

            # Fetch questions for response
            questions = db.query(ChatbotQuestion).filter(
                ChatbotQuestion.chatbot_id == chatbot_id,
                ChatbotQuestion.tenant_id == tenant_id
            ).all()

            # Fetch knowledgebase IDs for response
            kb_assocs = db.query(ChatbotKnowledgebase).filter(
                ChatbotKnowledgebase.chatbot_id == chatbot_id,
                ChatbotKnowledgebase.tenant_id == tenant_id
            ).all()

            logger.info(f"Updated chatbot {chatbot_id} for tenant {tenant_id}")

            # Build connected account response
            connected_account = None
            if (chatbot.connected_account_display_name or
                chatbot.entity_type or
                chatbot.connected_account_id):
                connected_account = {
                    "displayName": chatbot.connected_account_display_name,
                    "entityType": chatbot.entity_type,
                    "accountId": chatbot.connected_account_id
                }

            return {
                "id": chatbot.id,
                "tenant_id": chatbot.tenant_id,
                "name": chatbot.name,
                "description": chatbot.description,
                "welcomeMessage": chatbot.welcome_message,
                "thankYouMessage": chatbot.thank_you_message,
                "connectedAccount": connected_account,
                "trigger": chatbot.trigger,
                "questions": [{"id": q.id, "question": q.question, "fieldId": q.field_id, "displayName": q.display_name} for q in questions],
                "knowledgebase_ids": [kb.document_id for kb in kb_assocs],
                "updated_at": chatbot.updated_at
            }

        except HTTPException:
            raise
        except Exception as e:
            if db:
                db.rollback()
            logger.error(f"Error updating chatbot {chatbot_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error updating chatbot: {str(e)}")
        finally:
            if db:
                db.close()

    def delete_chatbot(self, chatbot_id: str, tenant_id: str) -> Dict[str, Any]:
        """
        Delete a chatbot and all its associated data

        Args:
            chatbot_id: The chatbot ID to delete
            tenant_id: The tenant ID

        Returns:
            Dict[str, Any]: Deletion confirmation message
        """
        db = None
        try:
            db = next(get_db())

            # Check if chatbot exists
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()

            if not chatbot:
                raise HTTPException(status_code=404, detail="Chatbot not found")

            # Delete questions
            db.query(ChatbotQuestion).filter(
                ChatbotQuestion.chatbot_id == chatbot_id,
                ChatbotQuestion.tenant_id == tenant_id
            ).delete()

            # Delete knowledgebase associations
            db.query(ChatbotKnowledgebase).filter(
                ChatbotKnowledgebase.chatbot_id == chatbot_id,
                ChatbotKnowledgebase.tenant_id == tenant_id
            ).delete()

            # Delete chatbot
            db.delete(chatbot)
            db.commit()

            logger.info(f"Deleted chatbot {chatbot_id} for tenant {tenant_id}")

            return {
                "message": "Chatbot deleted successfully",
                "chatbot_id": chatbot_id,
                "tenant_id": tenant_id
            }

        except HTTPException:
            raise
        except Exception as e:
            if db:
                db.rollback()
            logger.error(f"Error deleting chatbot {chatbot_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error deleting chatbot: {str(e)}")
        finally:
            if db:
                db.close()

    def get_chatbot_with_details(self, chatbot_id: str, tenant_id: str) -> Dict[str, Any]:
        """
        Get a chatbot with all its details including questions and knowledgebase associations

        Args:
            chatbot_id: The chatbot ID
            tenant_id: The tenant ID

        Returns:
            Dict[str, Any]: Chatbot details
        """
        db = None
        try:
            db = next(get_db())

            # Check if chatbot exists
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()

            if not chatbot:
                raise HTTPException(status_code=404, detail="Chatbot not found")

            # Fetch questions for response
            questions = db.query(ChatbotQuestion).filter(
                ChatbotQuestion.chatbot_id == chatbot_id,
                ChatbotQuestion.tenant_id == tenant_id
            ).all()

            # Fetch knowledgebase IDs for response
            kb_assocs = db.query(ChatbotKnowledgebase).filter(
                ChatbotKnowledgebase.chatbot_id == chatbot_id,
                ChatbotKnowledgebase.tenant_id == tenant_id
            ).all()

            logger.info(f"Retrieved chatbot {chatbot_id} for tenant {tenant_id}")

            # Build connected account response
            connected_account = None
            if (chatbot.connected_account_display_name or
                chatbot.entity_type or
                chatbot.connected_account_id):
                connected_account = {
                    "displayName": chatbot.connected_account_display_name,
                    "entityType": chatbot.entity_type,
                    "accountId": chatbot.connected_account_id
                }

            return {
                "id": chatbot.id,
                "tenant_id": chatbot.tenant_id,
                "name": chatbot.name,
                "type": chatbot.type,
                "description": chatbot.description,
                "welcomeMessage": chatbot.welcome_message,
                "thankYouMessage": chatbot.thank_you_message,
                "connectedAccount": connected_account,
                "trigger": chatbot.trigger,
                "status": chatbot.status,
                "questions": [{"id": q.id, "question": q.question, "fieldId": q.field_id, "displayName": q.display_name} for q in questions],
                "knowledgebase_ids": [kb.document_id for kb in kb_assocs],
                "created_at": chatbot.created_at,
                "updated_at": chatbot.updated_at
            }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting chatbot {chatbot_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error getting chatbot: {str(e)}")
        finally:
            if db:
                db.close()

    def delete_question(self, chatbot_id: str, question_id: str, tenant_id: str) -> Dict[str, Any]:
        """
        Delete a specific question from a chatbot

        Args:
            chatbot_id: The chatbot ID
            question_id: The question ID to delete
            tenant_id: The tenant ID

        Returns:
            Dict[str, Any]: Deletion confirmation message
        """
        db = None
        try:
            db = next(get_db())

            # Check if question exists
            question = db.query(ChatbotQuestion).filter(
                ChatbotQuestion.id == question_id,
                ChatbotQuestion.chatbot_id == chatbot_id,
                ChatbotQuestion.tenant_id == tenant_id
            ).first()

            if not question:
                raise HTTPException(status_code=404, detail="Question not found")

            # Delete question
            db.delete(question)
            db.commit()

            logger.info(f"Deleted question {question_id} from chatbot {chatbot_id} for tenant {tenant_id}")

            return {
                "message": "Question deleted successfully",
                "question_id": question_id,
                "chatbot_id": chatbot_id,
                "tenant_id": tenant_id
            }

        except HTTPException:
            raise
        except Exception as e:
            if db:
                db.rollback()
            logger.error(f"Error deleting question {question_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error deleting question: {str(e)}")
        finally:
            if db:
                db.close()

    def configure_chatbot_questions(self, chatbot_id: str, questions: List[QuestionCreate], tenant_id: str) -> Dict[str, Any]:
        """
        Configure questions for a chatbot (replace all existing questions)

        Args:
            chatbot_id: The chatbot ID
            questions: List of QuestionCreate objects
            tenant_id: The tenant ID

        Returns:
            Dict[str, Any]: Configuration result with questions
        """
        db = None
        try:
            db = next(get_db())

            # Check if chatbot exists
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()

            if not chatbot:
                raise HTTPException(status_code=404, detail="Chatbot not found")

            # Delete existing questions
            db.query(ChatbotQuestion).filter(
                ChatbotQuestion.chatbot_id == chatbot_id,
                ChatbotQuestion.tenant_id == tenant_id
            ).delete()

            # Add new questions
            created_questions = []
            for q_data in questions:
                question = ChatbotQuestion(
                    id=str(uuid.uuid4()),
                    chatbot_id=chatbot_id,
                    tenant_id=tenant_id,
                    question=q_data.question,
                    field_id=q_data.fieldId,
                    display_name=q_data.displayName
                )
                db.add(question)
                created_questions.append(question)

            # Update chatbot status to ACTIVE if it was DRAFT
            if chatbot.status == "DRAFT":
                chatbot.status = "ACTIVE"

            db.commit()

            # Refresh to get the created questions with their IDs
            for question in created_questions:
                db.refresh(question)

            logger.info(f"Configured {len(questions)} questions for chatbot {chatbot_id} for tenant {tenant_id}")

            return {
                "message": "Questions configured successfully",
                "chatbot_id": chatbot_id,
                "tenant_id": tenant_id,
                "questions": [{"id": q.id, "question": q.question, "fieldId": q.field_id, "displayName": q.display_name} for q in created_questions],
                "status": chatbot.status
            }

        except HTTPException:
            raise
        except Exception as e:
            if db:
                db.rollback()
            logger.error(f"Error configuring questions for chatbot {chatbot_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error configuring questions: {str(e)}")
        finally:
            if db:
                db.close()

    def list_chatbots(self, tenant_id: str, include_draft: bool = True) -> List[Dict[str, Any]]:
        """
        List all chatbots for a tenant

        Args:
            tenant_id: The tenant ID
            include_draft: Whether to include DRAFT status chatbots

        Returns:
            List[Dict[str, Any]]: List of chatbots with basic information
        """
        db = None
        try:
            db = next(get_db())

            query = db.query(Chatbot).filter(Chatbot.tenant_id == tenant_id)

            if not include_draft:
                query = query.filter(Chatbot.status != "DRAFT")

            chatbots = query.all()

            result = []
            for chatbot in chatbots:
                # Get question count for each chatbot
                question_count = db.query(ChatbotQuestion).filter(
                    ChatbotQuestion.chatbot_id == chatbot.id,
                    ChatbotQuestion.tenant_id == tenant_id
                ).count()

                # Get knowledgebase count for each chatbot
                kb_count = db.query(ChatbotKnowledgebase).filter(
                    ChatbotKnowledgebase.chatbot_id == chatbot.id,
                    ChatbotKnowledgebase.tenant_id == tenant_id
                ).count()

                result.append({
                    "id": chatbot.id,
                    "tenant_id": chatbot.tenant_id,
                    "name": chatbot.name,
                    "type": chatbot.type,
                    "description": chatbot.description,
                    "status": chatbot.status,
                    "question_count": question_count,
                    "knowledgebase_count": kb_count,
                    "created_at": chatbot.created_at,
                    "updated_at": chatbot.updated_at
                })

            logger.info(f"Listed {len(result)} chatbots for tenant {tenant_id} (include_draft: {include_draft})")

            return result

        except Exception as e:
            logger.error(f"Error listing chatbots for tenant {tenant_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error listing chatbots: {str(e)}")
        finally:
            if db:
                db.close()

    def get_chatbot_for_conversation(self, chatbot_id: str, tenant_id: str) -> Optional[Chatbot]:
        """
        Get a chatbot for conversation purposes (used by conversation endpoints)

        Args:
            chatbot_id: The chatbot ID
            tenant_id: The tenant ID

        Returns:
            Optional[Chatbot]: The chatbot if found, None otherwise
        """
        db = None
        try:
            db = next(get_db())

            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()

            if chatbot:
                logger.info(f"Retrieved chatbot {chatbot_id} for conversation")
            else:
                logger.warning(f"Chatbot {chatbot_id} not found for tenant {tenant_id}")

            return chatbot

        except Exception as e:
            logger.error(f"Error getting chatbot {chatbot_id} for conversation: {str(e)}")
            raise
        finally:
            if db:
                db.close()

    def get_chatbot_questions_for_conversation(self, chatbot_id: str, tenant_id: str) -> List[ChatbotQuestion]:
        """
        Get chatbot questions for conversation purposes

        Args:
            chatbot_id: The chatbot ID
            tenant_id: The tenant ID

        Returns:
            List[ChatbotQuestion]: List of questions for the chatbot
        """
        db = None
        try:
            db = next(get_db())

            questions = db.query(ChatbotQuestion).filter(
                ChatbotQuestion.chatbot_id == chatbot_id,
                ChatbotQuestion.tenant_id == tenant_id
            ).all()

            logger.info(f"Retrieved {len(questions)} questions for chatbot {chatbot_id}")

            return questions

        except Exception as e:
            logger.error(f"Error getting questions for chatbot {chatbot_id}: {str(e)}")
            raise
        finally:
            if db:
                db.close()

    def find_chatbot_by_entity_and_trigger(self, entity_type: str, connected_account_id: int, trigger: str, tenant_id: str) -> Optional[Chatbot]:
        """
        Find chatbot by entity type, connected account ID, and trigger

        Args:
            entity_type: The entity type (e.g., 'LEAD')
            connected_account_id: The connected account ID
            trigger: The trigger ('NEW_ENTITY' or 'EXISTING_ENTITY')
            tenant_id: The tenant ID

        Returns:
            Optional[Chatbot]: The chatbot model object or None if not found
        """
        db = None
        try:
            db = next(get_db())

            chatbot = db.query(Chatbot).filter(
                Chatbot.entity_type == entity_type,
                Chatbot.connected_account_id == connected_account_id,
                Chatbot.trigger == trigger,
                Chatbot.tenant_id == tenant_id,
                Chatbot.status == "ACTIVE"
            ).first()

            if chatbot:
                logger.info(f"Found chatbot {chatbot.id} for entity_type={entity_type}, account_id={connected_account_id}, trigger={trigger}")
            else:
                logger.warning(f"No chatbot found for entity_type={entity_type}, account_id={connected_account_id}, trigger={trigger}, tenant={tenant_id}")

            return chatbot

        except Exception as e:
            logger.error(f"Error finding chatbot by entity and trigger: {str(e)}")
            raise
        finally:
            if db:
                db.close()

    def has_knowledgebase(self, chatbot_id: str, tenant_id: str) -> bool:
        """
        Check if a chatbot has any knowledgebase documents

        Args:
            chatbot_id: The chatbot ID
            tenant_id: The tenant ID

        Returns:
            bool: True if chatbot has knowledgebase, False otherwise
        """
        db = None
        try:
            db = next(get_db())

            kb_count = db.query(ChatbotKnowledgebase).filter(
                ChatbotKnowledgebase.chatbot_id == chatbot_id,
                ChatbotKnowledgebase.tenant_id == tenant_id
            ).count()

            logger.info(f"Chatbot {chatbot_id} has {kb_count} knowledgebase documents")
            return kb_count > 0

        except Exception as e:
            logger.error(f"Error checking knowledgebase for chatbot {chatbot_id}: {str(e)}")
            return False
        finally:
            if db:
                db.close()

    def update_all_chatbot_questions(self, chatbot_id: str, questions: List[QuestionCreate], tenant_id: str) -> Dict[str, Any]:
        """
        Update all questions for a chatbot (replaces existing questions)

        Args:
            chatbot_id: The chatbot ID
            questions: List of QuestionCreate objects
            tenant_id: The tenant ID

        Returns:
            Dict[str, Any]: Update result with questions
        """
        # This is the same as configure_chatbot_questions
        return self.configure_chatbot_questions(chatbot_id, questions, tenant_id)

    def update_chatbot_question(self, chatbot_id: str, question_id: str, question_data: QuestionUpdate, tenant_id: str) -> Dict[str, Any]:
        """
        Update a specific question for a chatbot

        Args:
            chatbot_id: The chatbot ID
            question_id: The question ID to update
            question_data: QuestionUpdate object with new data
            tenant_id: The tenant ID

        Returns:
            Dict[str, Any]: Updated question information
        """
        db = None
        try:
            db = next(get_db())

            # Check if chatbot exists
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()

            if not chatbot:
                raise HTTPException(status_code=404, detail="Chatbot not found")

            # Check if question exists
            question = db.query(ChatbotQuestion).filter(
                ChatbotQuestion.id == question_id,
                ChatbotQuestion.chatbot_id == chatbot_id,
                ChatbotQuestion.tenant_id == tenant_id
            ).first()

            if not question:
                raise HTTPException(status_code=404, detail="Question not found")

            # Update question fields if provided
            if question_data.question is not None:
                question.question = question_data.question
            if question_data.fieldId is not None:
                question.field_id = question_data.fieldId
            if question_data.displayName is not None:
                question.display_name = question_data.displayName

            db.commit()
            db.refresh(question)

            logger.info(f"Updated question {question_id} for chatbot {chatbot_id}")

            return {
                "message": "Question updated successfully",
                "question": {
                    "id": question.id,
                    "question": question.question,
                    "fieldId": question.field_id,
                    "displayName": question.display_name
                }
            }

        except HTTPException:
            raise
        except Exception as e:
            if db:
                db.rollback()
            logger.error(f"Error updating question {question_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error updating question: {str(e)}")
        finally:
            if db:
                db.close()

    def get_chatbot_knowledgebase(self, chatbot_id: str, tenant_id: str) -> Dict[str, Any]:
        """
        Get all knowledgebase documents for a chatbot

        Args:
            chatbot_id: The chatbot ID
            tenant_id: The tenant ID

        Returns:
            Dict[str, Any]: List of knowledgebase documents
        """
        db = None
        try:
            db = next(get_db())

            # Check if chatbot exists
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()

            if not chatbot:
                raise HTTPException(status_code=404, detail="Chatbot not found")

            # Get knowledgebase associations
            kb_assocs = db.query(ChatbotKnowledgebase).filter(
                ChatbotKnowledgebase.chatbot_id == chatbot_id,
                ChatbotKnowledgebase.tenant_id == tenant_id
            ).all()

            # Get document details
            documents = []
            for kb in kb_assocs:
                document = db.query(Document).filter(
                    Document.id == kb.document_id,
                    Document.tenant_id == tenant_id
                ).first()
                if document:
                    documents.append({
                        "id": document.id,
                        "document_name": document.document_name,
                        "document_type": document.document_type,
                        "created_at": document.created_at,
                        "created_by": document.created_by
                    })

            logger.info(f"Retrieved {len(documents)} knowledgebase documents for chatbot {chatbot_id}")

            return {
                "chatbot_id": chatbot_id,
                "documents": documents,
                "total_count": len(documents)
            }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting knowledgebase for chatbot {chatbot_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error getting knowledgebase: {str(e)}")
        finally:
            if db:
                db.close()

    async def upload_multiple_knowledgebase_files(
        self,
        chatbot_id: str,
        files: List[UploadFile],
        tenant_id: str,
        user_id: str
    ) -> Dict[str, Any]:
        """
        Upload multiple files to chatbot knowledgebase

        Args:
            chatbot_id: The chatbot ID
            files: List of UploadFile objects
            tenant_id: The tenant ID
            user_id: The user ID for audit trail

        Returns:
            Dict[str, Any]: Upload results with success and failure details
        """
        db = None
        successful_uploads = []
        failed_uploads = []

        try:
            db = next(get_db())

            # Check if chatbot exists
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()

            if not chatbot:
                raise HTTPException(status_code=404, detail="Chatbot not found")

            # Initialize services
            s3_service = S3Service()
            es_service = ElasticsearchService()

            # Process each file
            for file in files:
                file_result = {
                    "filename": file.filename,
                    "status": "processing"
                }

                try:
                    # Validate file type
                    if not file.filename.lower().endswith('.pdf'):
                        file_result.update({
                            "status": "failed",
                            "error": "Only PDF files are supported"
                        })
                        failed_uploads.append(file_result)
                        continue

                    # Read file content
                    file_data = await file.read()

                    if not file_data:
                        file_result.update({
                            "status": "failed",
                            "error": "File is empty"
                        })
                        failed_uploads.append(file_result)
                        continue

                    # Extract text from PDF
                    pdf_content = await self._extract_pdf_text(file_data, file.filename)

                    # Generate unique document ID
                    document_id = str(uuid.uuid4())

                    # Upload file to S3
                    file_data_io = BytesIO(file_data)
                    s3_key = s3_service.upload_file(
                        file_data=file_data_io,
                        tenant_id=tenant_id,
                        chatbot_id=chatbot_id,
                        filename=file.filename
                    )

                    # Clean and index content in Elasticsearch using new master index strategy
                    cleaned_content = es_service.clean_text_for_embedding(pdf_content)
                    es_index = es_service.index_document(
                        tenant_id,
                        document_id,
                        cleaned_content,
                        chatbot_id,  # chatbot_id
                        300,  # chunk_size
                        30,   # overlap
                        db    # database session for master index strategy
                    )

                    # Store document metadata in PostgreSQL
                    document = Document(
                        id=document_id,
                        tenant_id=tenant_id,
                        document_name=file.filename,
                        document_type="pdf",
                        es_index=es_index,
                        es_document_id=document_id,
                        s3_key=s3_key,
                        created_by=user_id
                    )
                    db.add(document)

                    # Create association between chatbot and document
                    kb_assoc = ChatbotKnowledgebase(
                        id=str(uuid.uuid4()),
                        chatbot_id=chatbot_id,
                        document_id=document_id,
                        tenant_id=tenant_id
                    )
                    db.add(kb_assoc)

                    # Update file result with success
                    file_result.update({
                        "status": "success",
                        "document_id": document_id,
                        "s3_key": s3_key,
                        "es_index": es_index
                    })
                    successful_uploads.append(file_result)

                    logger.info(f"Successfully processed file {file.filename} for chatbot {chatbot_id}")

                except Exception as e:
                    logger.error(f"Error processing file {file.filename}: {str(e)}")
                    file_result.update({
                        "status": "failed",
                        "error": str(e)
                    })
                    failed_uploads.append(file_result)
                    continue

            # Update chatbot status if it was in DRAFT and has questions
            if chatbot.status == "DRAFT" and successful_uploads:
                question_count = db.query(ChatbotQuestion).filter(
                    ChatbotQuestion.chatbot_id == chatbot_id,
                    ChatbotQuestion.tenant_id == tenant_id
                ).count()

                if question_count > 0:
                    chatbot.status = "ACTIVE"
                    logger.info(f"Updated chatbot {chatbot_id} status to ACTIVE")

            # Commit all successful changes
            if successful_uploads:
                db.commit()
                logger.info(f"Committed {len(successful_uploads)} successful uploads for chatbot {chatbot_id}")

            return {
                "message": f"Processed {len(files)} files",
                "chatbot_id": chatbot_id,
                "tenant_id": tenant_id,
                "total_files": len(files),
                "successful_uploads": len(successful_uploads),
                "failed_uploads": len(failed_uploads),
                "results": {
                    "successful": successful_uploads,
                    "failed": failed_uploads
                },
                "chatbot_status": chatbot.status
            }

        except HTTPException:
            raise
        except Exception as e:
            if db:
                db.rollback()
            logger.error(f"Error in multiple file upload for chatbot {chatbot_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error processing files: {str(e)}")
        finally:
            if db:
                db.close()

    def remove_knowledgebase_document(self, chatbot_id: str, document_id: str, tenant_id: str) -> Dict[str, Any]:
        """
        Remove a specific document from chatbot knowledgebase

        Args:
            chatbot_id: The chatbot ID
            document_id: The document ID to remove
            tenant_id: The tenant ID

        Returns:
            Dict[str, Any]: Removal result
        """
        db = None
        try:
            db = next(get_db())

            # Check if chatbot exists
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()

            if not chatbot:
                raise HTTPException(status_code=404, detail="Chatbot not found")

            # Check if document exists and is associated with this chatbot
            kb_assoc = db.query(ChatbotKnowledgebase).filter(
                ChatbotKnowledgebase.chatbot_id == chatbot_id,
                ChatbotKnowledgebase.document_id == document_id,
                ChatbotKnowledgebase.tenant_id == tenant_id
            ).first()

            if not kb_assoc:
                raise HTTPException(status_code=404, detail="Document not found in chatbot knowledgebase")

            # Get document details for cleanup
            document = db.query(Document).filter(
                Document.id == document_id,
                Document.tenant_id == tenant_id
            ).first()

            if not document:
                raise HTTPException(status_code=404, detail="Document not found")

            # Remove the association
            db.delete(kb_assoc)

            # Check if this document is used by other chatbots
            other_associations = db.query(ChatbotKnowledgebase).filter(
                ChatbotKnowledgebase.document_id == document_id,
                ChatbotKnowledgebase.tenant_id == tenant_id,
                ChatbotKnowledgebase.chatbot_id != chatbot_id
            ).count()

            # If no other chatbots use this document, remove it completely
            if other_associations == 0:
                # TODO: Remove from Elasticsearch
                # TODO: Remove from S3
                # For now, just remove from database
                db.delete(document)
                logger.info(f"Removed document {document_id} completely as it's not used by other chatbots")
            else:
                logger.info(f"Document {document_id} is still used by {other_associations} other chatbots")

            db.commit()

            logger.info(f"Removed document {document_id} from chatbot {chatbot_id} knowledgebase")

            return {
                "message": "Document removed from knowledgebase successfully",
                "chatbot_id": chatbot_id,
                "document_id": document_id,
                "document_name": document.document_name,
                "completely_removed": other_associations == 0
            }

        except HTTPException:
            raise
        except Exception as e:
            if db:
                db.rollback()
            logger.error(f"Error removing document {document_id} from chatbot {chatbot_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error removing document: {str(e)}")
        finally:
            if db:
                db.close()

    async def _extract_pdf_text(self, file_data: bytes, filename: str) -> str:
        """
        Extract text content from PDF file data

        Args:
            file_data: PDF file bytes
            filename: Original filename for error reporting

        Returns:
            str: Extracted text content
        """
        try:
            pdf_reader = PyPDF2.PdfReader(BytesIO(file_data))
            pdf_content = ""

            # Extract text from each page
            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                pdf_content += page.extract_text() + "\n\n"

            if not pdf_content.strip():
                raise ValueError("Could not extract text from PDF")

            return pdf_content

        except Exception as e:
            logger.error(f"Error extracting text from PDF {filename}: {str(e)}")
            raise ValueError(f"Error processing PDF {filename}: {str(e)}")
