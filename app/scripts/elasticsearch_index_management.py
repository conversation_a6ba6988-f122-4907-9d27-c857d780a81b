#!/usr/bin/env python3
"""
Elasticsearch Index Management Script

This script provides utilities for managing the master index strategy for the WhatsApp chatbot.
It includes functions for:
- Creating and managing master indices
- Setting up tenant aliases
- Migrating from old tenant-specific indices to new master index strategy
- Monitoring index statistics and health
"""

import os
import sys
import argparse
import logging
from typing import Dict, List

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.services.elasticsearch_index_manager import ElasticsearchIndexManager
from app.database import get_db
from app.models import TenantIndexMapping
from sqlalchemy.orm import Session

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ElasticsearchIndexManagementCLI:
    """Command-line interface for Elasticsearch index management."""
    
    def __init__(self):
        self.index_manager = ElasticsearchIndexManager()
    
    def create_master_indices(self, count: int = 1) -> None:
        """Create one or more master indices."""
        logger.info(f"Creating {count} master indices...")
        
        for i in range(1, count + 1):
            master_index = self.index_manager.get_master_index_name(i)
            self.index_manager._ensure_master_index_exists(master_index)
            logger.info(f"Created master index: {master_index}")
    
    def show_statistics(self) -> None:
        """Display statistics about tenant distribution and index usage."""
        db: Session = next(get_db())
        try:
            stats = self.index_manager.get_tenant_statistics(db)
            
            print("\n" + "="*60)
            print("ELASTICSEARCH INDEX STATISTICS")
            print("="*60)
            print(f"Total Master Indices: {stats['total_master_indices']}")
            print(f"Total Tenants: {stats['total_tenants']}")
            print()
            
            if stats['indices']:
                print("Master Index Details:")
                print("-" * 60)
                for index_name, details in stats['indices'].items():
                    print(f"Index: {index_name}")
                    print(f"  Tenants: {details['tenant_count']}/{self.index_manager.max_tenants_per_index}")
                    print(f"  Available Slots: {details['available_slots']}")
                    print(f"  Utilization: {details['utilization_percentage']:.1f}%")
                    print()
            else:
                print("No master indices found.")
            
            print("="*60)
        finally:
            db.close()
    
    def migrate_tenant_indices(self, dry_run: bool = False) -> None:
        """Migrate existing tenant-specific indices to master index strategy."""
        db: Session = next(get_db())
        try:
            if dry_run:
                logger.info("DRY RUN: Simulating migration...")
            else:
                logger.info("Starting migration of tenant indices...")
            
            if not dry_run:
                results = self.index_manager.migrate_existing_tenant_indices(db)
            else:
                # For dry run, just show what would be migrated
                all_indices = self.index_manager.es_client.indices.get("*")
                old_tenant_indices = [
                    index for index in all_indices.keys() 
                    if index.startswith("kb-") and not index.startswith(self.index_manager.master_index_prefix)
                ]
                
                results = {
                    "migrated": [],
                    "errors": [],
                    "skipped": []
                }
                
                for old_index in old_tenant_indices:
                    tenant_id = old_index.replace("kb-", "")
                    alias_name = self.index_manager.get_tenant_alias(tenant_id)
                    results["migrated"].append({
                        "tenant_id": tenant_id,
                        "old_index": old_index,
                        "new_alias": alias_name,
                        "documents_migrated": "N/A (dry run)"
                    })
            
            # Display results
            print("\n" + "="*60)
            print("MIGRATION RESULTS")
            print("="*60)
            
            if results["migrated"]:
                print(f"Successfully migrated {len(results['migrated'])} tenant indices:")
                for result in results["migrated"]:
                    print(f"  {result['old_index']} -> {result['new_alias']} "
                          f"({result['documents_migrated']} documents)")
            
            if results["errors"]:
                print(f"\nErrors during migration ({len(results['errors'])}):")
                for error in results["errors"]:
                    print(f"  {error['old_index']}: {error['error']}")
            
            if results["skipped"]:
                print(f"\nSkipped indices ({len(results['skipped'])}):")
                for skipped in results["skipped"]:
                    print(f"  {skipped}")
            
            print("="*60)
            
        except Exception as e:
            logger.error(f"Migration failed: {str(e)}")
            raise
        finally:
            db.close()
    
    def create_tenant_alias(self, tenant_id: str) -> None:
        """Create an alias for a specific tenant."""
        db: Session = next(get_db())
        try:
            alias_name = self.index_manager.get_or_create_tenant_alias(tenant_id, db)
            logger.info(f"Created tenant alias: {alias_name}")
        finally:
            db.close()
    
    def list_tenant_mappings(self) -> None:
        """List all tenant-to-index mappings."""
        db: Session = next(get_db())
        try:
            mappings = db.query(TenantIndexMapping).all()
            
            print("\n" + "="*80)
            print("TENANT INDEX MAPPINGS")
            print("="*80)
            print(f"{'Tenant ID':<20} {'Master Index':<20} {'Alias Name':<25} {'Created':<15}")
            print("-" * 80)
            
            for mapping in mappings:
                master_index = self.index_manager.get_master_index_name(mapping.master_index_number)
                created = mapping.created_at.strftime("%Y-%m-%d") if mapping.created_at else "N/A"
                print(f"{mapping.tenant_id:<20} {master_index:<20} {mapping.alias_name:<25} {created:<15}")
            
            print("="*80)
            print(f"Total mappings: {len(mappings)}")
            
        finally:
            db.close()
    
    def health_check(self) -> None:
        """Perform a health check on the Elasticsearch cluster and indices."""
        try:
            # Check cluster health
            cluster_health = self.index_manager.es_client.cluster.health()
            
            print("\n" + "="*60)
            print("ELASTICSEARCH HEALTH CHECK")
            print("="*60)
            print(f"Cluster Status: {cluster_health['status']}")
            print(f"Number of Nodes: {cluster_health['number_of_nodes']}")
            print(f"Active Primary Shards: {cluster_health['active_primary_shards']}")
            print(f"Active Shards: {cluster_health['active_shards']}")
            print(f"Relocating Shards: {cluster_health['relocating_shards']}")
            print(f"Initializing Shards: {cluster_health['initializing_shards']}")
            print(f"Unassigned Shards: {cluster_health['unassigned_shards']}")
            
            # Check master indices
            all_indices = self.index_manager.es_client.indices.get("*")
            master_indices = [
                index for index in all_indices.keys() 
                if index.startswith(self.index_manager.master_index_prefix)
            ]
            
            print(f"\nMaster Indices Found: {len(master_indices)}")
            for index in master_indices:
                index_stats = self.index_manager.es_client.indices.stats(index=index)
                doc_count = index_stats['indices'][index]['total']['docs']['count']
                size_bytes = index_stats['indices'][index]['total']['store']['size_in_bytes']
                size_mb = size_bytes / (1024 * 1024)
                print(f"  {index}: {doc_count} documents, {size_mb:.2f} MB")
            
            print("="*60)
            
        except Exception as e:
            logger.error(f"Health check failed: {str(e)}")
            raise

def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(description="Elasticsearch Index Management for WhatsApp Chatbot")
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Create master indices command
    create_parser = subparsers.add_parser('create-masters', help='Create master indices')
    create_parser.add_argument('--count', type=int, default=1, help='Number of master indices to create')
    
    # Show statistics command
    subparsers.add_parser('stats', help='Show index statistics')
    
    # Migration command
    migrate_parser = subparsers.add_parser('migrate', help='Migrate tenant indices to master index strategy')
    migrate_parser.add_argument('--dry-run', action='store_true', help='Simulate migration without making changes')
    
    # Create tenant alias command
    tenant_parser = subparsers.add_parser('create-alias', help='Create alias for a tenant')
    tenant_parser.add_argument('tenant_id', help='Tenant ID to create alias for')
    
    # List mappings command
    subparsers.add_parser('list-mappings', help='List all tenant-to-index mappings')
    
    # Health check command
    subparsers.add_parser('health', help='Perform health check on Elasticsearch')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    cli = ElasticsearchIndexManagementCLI()
    
    try:
        if args.command == 'create-masters':
            cli.create_master_indices(args.count)
        elif args.command == 'stats':
            cli.show_statistics()
        elif args.command == 'migrate':
            cli.migrate_tenant_indices(args.dry_run)
        elif args.command == 'create-alias':
            cli.create_tenant_alias(args.tenant_id)
        elif args.command == 'list-mappings':
            cli.list_tenant_mappings()
        elif args.command == 'health':
            cli.health_check()
    except Exception as e:
        logger.error(f"Command failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
