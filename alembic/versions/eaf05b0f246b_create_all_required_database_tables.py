"""Create all required database tables

Revision ID: eaf05b0f246b
Revises: 
Create Date: 2025-07-09 10:53:42.386285

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'eaf05b0f246b'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('chatbot_knowledgebases',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('chatbot_id', sa.String(), nullable=False),
    sa.Column('document_id', sa.String(), nullable=False),
    sa.Column('tenant_id', sa.String(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_chatbot_knowledgebases_chatbot_id'), 'chatbot_knowledgebases', ['chatbot_id'], unique=False)
    op.create_index(op.f('ix_chatbot_knowledgebases_document_id'), 'chatbot_knowledgebases', ['document_id'], unique=False)
    op.create_index(op.f('ix_chatbot_knowledgebases_tenant_id'), 'chatbot_knowledgebases', ['tenant_id'], unique=False)
    op.create_table('chatbot_questions',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('chatbot_id', sa.String(), nullable=False),
    sa.Column('tenant_id', sa.String(), nullable=False),
    sa.Column('question', sa.String(length=100), nullable=False),
    sa.Column('field_id', sa.Integer(), nullable=False),
    sa.Column('display_name', sa.String(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_chatbot_questions_chatbot_id'), 'chatbot_questions', ['chatbot_id'], unique=False)
    op.create_index(op.f('ix_chatbot_questions_tenant_id'), 'chatbot_questions', ['tenant_id'], unique=False)
    op.create_table('chatbots',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('tenant_id', sa.String(), nullable=True),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('type', sa.String(), nullable=True),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('welcome_message', sa.String(), nullable=True),
    sa.Column('thank_you_message', sa.String(), nullable=True),
    sa.Column('connected_account_display_name', sa.String(), nullable=True),
    sa.Column('entity_type', sa.String(), nullable=True),
    sa.Column('connected_account_id', sa.Integer(), nullable=True),
    sa.Column('trigger', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_chatbots_id'), 'chatbots', ['id'], unique=False)
    op.create_index(op.f('ix_chatbots_tenant_id'), 'chatbots', ['tenant_id'], unique=False)
    op.create_table('documents',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('tenant_id', sa.String(), nullable=False),
    sa.Column('document_name', sa.String(), nullable=False),
    sa.Column('document_type', sa.String(), nullable=False),
    sa.Column('es_index', sa.String(), nullable=True),
    sa.Column('es_document_id', sa.String(), nullable=True),
    sa.Column('s3_key', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_by', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('chatbot_conversations',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('chatbot_id', sa.String(), nullable=True),
    sa.Column('tenant_id', sa.String(), nullable=True),
    sa.Column('user_id', sa.String(), nullable=True),
    sa.Column('conversation_data', sa.Text(), nullable=True),
    sa.Column('completed', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['chatbot_id'], ['chatbots.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_chatbot_conversations_id'), 'chatbot_conversations', ['id'], unique=False)
    op.create_index(op.f('ix_chatbot_conversations_tenant_id'), 'chatbot_conversations', ['tenant_id'], unique=False)
    op.create_index(op.f('ix_chatbot_conversations_user_id'), 'chatbot_conversations', ['user_id'], unique=False)
    op.create_table('chatbot_credit_usage',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('chatbot_id', sa.String(), nullable=True),
    sa.Column('conversation_id', sa.String(), nullable=True),
    sa.Column('tenant_id', sa.String(), nullable=True),
    sa.Column('question', sa.String(), nullable=False),
    sa.Column('answer', sa.String(), nullable=False),
    sa.Column('credits_used', sa.Integer(), nullable=True),
    sa.Column('has_knowledgebase', sa.Boolean(), nullable=True),
    sa.Column('timestamp', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['chatbot_id'], ['chatbots.id'], ),
    sa.ForeignKeyConstraint(['conversation_id'], ['chatbot_conversations.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_chatbot_credit_usage_chatbot_id'), 'chatbot_credit_usage', ['chatbot_id'], unique=False)
    op.create_index(op.f('ix_chatbot_credit_usage_conversation_id'), 'chatbot_credit_usage', ['conversation_id'], unique=False)
    op.create_index(op.f('ix_chatbot_credit_usage_tenant_id'), 'chatbot_credit_usage', ['tenant_id'], unique=False)
    op.create_table('conversation_token_usage',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('conversation_id', sa.String(), nullable=True),
    sa.Column('tenant_id', sa.String(), nullable=True),
    sa.Column('input', sa.JSON(), nullable=True),
    sa.Column('output', sa.JSON(), nullable=True),
    sa.Column('input_tokens', sa.Integer(), nullable=True),
    sa.Column('output_tokens', sa.Integer(), nullable=True),
    sa.Column('timestamp', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['conversation_id'], ['chatbot_conversations.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_conversation_token_usage_conversation_id'), 'conversation_token_usage', ['conversation_id'], unique=False)
    op.create_index(op.f('ix_conversation_token_usage_tenant_id'), 'conversation_token_usage', ['tenant_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_conversation_token_usage_tenant_id'), table_name='conversation_token_usage')
    op.drop_index(op.f('ix_conversation_token_usage_conversation_id'), table_name='conversation_token_usage')
    op.drop_table('conversation_token_usage')
    op.drop_index(op.f('ix_chatbot_credit_usage_tenant_id'), table_name='chatbot_credit_usage')
    op.drop_index(op.f('ix_chatbot_credit_usage_conversation_id'), table_name='chatbot_credit_usage')
    op.drop_index(op.f('ix_chatbot_credit_usage_chatbot_id'), table_name='chatbot_credit_usage')
    op.drop_table('chatbot_credit_usage')
    op.drop_index(op.f('ix_chatbot_conversations_user_id'), table_name='chatbot_conversations')
    op.drop_index(op.f('ix_chatbot_conversations_tenant_id'), table_name='chatbot_conversations')
    op.drop_index(op.f('ix_chatbot_conversations_id'), table_name='chatbot_conversations')
    op.drop_table('chatbot_conversations')
    op.drop_table('documents')
    op.drop_index(op.f('ix_chatbots_tenant_id'), table_name='chatbots')
    op.drop_index(op.f('ix_chatbots_id'), table_name='chatbots')
    op.drop_table('chatbots')
    op.drop_index(op.f('ix_chatbot_questions_tenant_id'), table_name='chatbot_questions')
    op.drop_index(op.f('ix_chatbot_questions_chatbot_id'), table_name='chatbot_questions')
    op.drop_table('chatbot_questions')
    op.drop_index(op.f('ix_chatbot_knowledgebases_tenant_id'), table_name='chatbot_knowledgebases')
    op.drop_index(op.f('ix_chatbot_knowledgebases_document_id'), table_name='chatbot_knowledgebases')
    op.drop_index(op.f('ix_chatbot_knowledgebases_chatbot_id'), table_name='chatbot_knowledgebases')
    op.drop_table('chatbot_knowledgebases')
    # ### end Alembic commands ###
