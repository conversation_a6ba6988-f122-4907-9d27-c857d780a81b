"""Add tenant index mapping table for Elasticsearch master index strategy

Revision ID: 2001b317c8db
Revises: eaf05b0f246b
Create Date: 2025-07-09 11:33:50.820286

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2001b317c8db'
down_revision: Union[str, None] = 'eaf05b0f246b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('tenant_index_mappings',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('tenant_id', sa.String(), nullable=False),
    sa.Column('master_index_number', sa.Integer(), nullable=False),
    sa.Column('alias_name', sa.String(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_tenant_index_mappings_alias_name'), 'tenant_index_mappings', ['alias_name'], unique=True)
    op.create_index(op.f('ix_tenant_index_mappings_master_index_number'), 'tenant_index_mappings', ['master_index_number'], unique=False)
    op.create_index(op.f('ix_tenant_index_mappings_tenant_id'), 'tenant_index_mappings', ['tenant_id'], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_tenant_index_mappings_tenant_id'), table_name='tenant_index_mappings')
    op.drop_index(op.f('ix_tenant_index_mappings_master_index_number'), table_name='tenant_index_mappings')
    op.drop_index(op.f('ix_tenant_index_mappings_alias_name'), table_name='tenant_index_mappings')
    op.drop_table('tenant_index_mappings')
    # ### end Alembic commands ###
