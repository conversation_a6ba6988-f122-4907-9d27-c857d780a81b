# Alembic Migration Dockerfile
# This container is specifically for running database migrations
FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user for security
RUN groupadd -r alembic && useradd -r -g alembic alembic

# Set working directory
WORKDIR /app

# Copy requirements (only what's needed for migrations)
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Copy alembic configuration and migration files
COPY --chown=alembic:alembic alembic/ ./alembic/
COPY --chown=alembic:alembic alembic.ini .
COPY --chown=alembic:alembic app/models.py ./app/
COPY --chown=alembic:alembic app/database.py ./app/
COPY --chown=alembic:alembic app/__init__.py ./app/

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONPATH=/app

# Create logs directory
RUN mkdir -p /app/logs && \
    chown -R alembic:alembic /app

# Switch to non-root user
USER alembic

# Default command - can be overridden
CMD ["alembic", "upgrade", "head"]

# Health check for migration container
HEALTHCHECK --interval=10s --timeout=5s --start-period=10s --retries=3 \
    CMD pg_isready -h ${DATABASE_HOST:-localhost} -p ${DATABASE_PORT:-5432} || exit 1

# Labels for better container management
LABEL maintainer="WhatsApp Chatbot Team"
LABEL description="Alembic database migration container"
LABEL version="1.0.0"
